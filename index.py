from dash import dcc, html, Output, Input
import dash_bootstrap_components as dbc
from app import app
from pages import analyze, portfolio, simulate
from components import navbar

nav = navbar.create_navbar("Portfolio Simulator", ["Portfolio", "Simulate", "Analyze"])

app.layout = html.Div([
    dcc.Location(id='url', refresh=False),
    nav,

    # Main content area with bottom padding to account for footer
    html.Div([
        dbc.Row(
            dbc.Col(
                html.Div(id='page-content'),
                width={"size":10, "offset":1},
            ),
            className="mt-3",   # Added margin-top using Bootstrap class
        ),
    ]),

    # Fixed Footer at bottom
    html.Footer([
        html.Hr(className="mb-0"),
        dbc.Container([
            dbc.Row([
                dbc.Col([
                    html.P([
                        "Portfolio Simulator - ",
                        html.A("View on GitHub",
                               href="https://github.com/meganwolf0/portfolio-simulator",
                               target="_blank",
                               className="text-decoration-none")
                    ], className="text-center text-muted mb-0")
                ], width=12)
            ])
        ], fluid=True)
    ], className="py-3 bg-light", style={
        "position": "fixed",
        "bottom": "0",
        "left": "0",
        "right": "0",
        "z-index": "1000"
    }),
])

# Page routing callback
@app.callback(Output('page-content', 'children'),
              Input('url', 'pathname'))
def display_page(pathname):
    if pathname == '/simulate':
        return simulate.layout
    elif pathname == '/analyze':
        return analyze.layout
    else:  # Default to portfolio
        return portfolio.layout

# Add callback to highlight active page
@app.callback(
    [Output('portfolio-link', 'className'),
     Output('simulate-link', 'className'),
     Output('analyze-link', 'className')],
    Input('url', 'pathname')
)
def update_nav_links(pathname):
    base_class = 'nav-link'
    active_class = 'nav-link active'
    
    if pathname == '/simulate':
        return base_class, active_class, base_class
    elif pathname == '/analyze':
        return base_class, base_class, active_class
    else:  # Default to portfolio
        return active_class, base_class, base_class

if __name__ == '__main__':
    app.run(debug=True)