from dash import dcc, html
import dash_bootstrap_components as dbc

def create_navbar(header, pages, active_page=None):
    if active_page==None:
        active_page = pages[0]

    # If active page is set, add 'active' class to the corresponding link
    nav_items = [dbc.NavItem(dbc.NavLink(page, href=f'/{page.lower()}', active=(page.lower() == active_page.lower()))) for page in pages]
    return dbc.NavbarSimple(
        children=nav_items,
        brand=header,
        brand_href="/",
        fluid=True,
        color="primary",
        dark=True,
        className="px-5",  # Added padding on left and right using Bootstrap class
    )