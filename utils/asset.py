import pandas as pd
import os
import json
from dataclasses import dataclass
from typing import Optional
from services.data_provider import DataProviderFactory

@dataclass
class AssetData:
    symbol: str
    price: float
    sectors: Optional[dict] = None
    geography: Optional[dict] = None
    top_holdings: Optional[dict] = None
    quote_type: str = 'EQUITY'

class Asset:
    """
    A single asset in the portfolio.
    """
    def __init__(self, sym, quantity=None, data_provider="yfinance"):
        self.base_quantity = quantity
        self.data_provider_name = data_provider
        self.data = AssetData(
            symbol=sym,
            price=0,
            sectors=None,
            geography=None,
            top_holdings=None
        )
        self.simulations = []

        self._get_or_update_data(from_cache=True)
    
    def add_simulation(self, name, quantity):
        """Add a simulation to the asset"""
        pass
    
    def write_to_cache(self):
        """Write symbol data to cache"""
        if self.data is None:
            return False
        
        os.makedirs('.cache', exist_ok=True)
        cache_path = f'.cache/{self.data.symbol}.json'
        with open(cache_path, 'w') as f:
            # Convert AssetData to dict for JSON serialization
            data_dict = {
                'symbol': self.data.symbol,
                'price': self.data.price,
                'sectors': self.data.sectors,
                'geography': self.data.geography,
                'top_holdings': self.data.top_holdings,
                'quote_type': self.data.quote_type
            }
            json.dump(data_dict, f, indent=2)
        return True
    
    def _get_or_update_data(self, from_cache=True):
        """Get data from cache or fetch from external data provider"""
        if from_cache and self._load_from_cache():
            return self.data

        if self._load_from_external_provider():
            self.write_to_cache()
            return self.data

        return None

    def _load_from_cache(self):
        """Load symbol data from cache"""
        cache_path = f'.cache/{self.data.symbol}.json'
        if os.path.exists(cache_path):
            with open(cache_path, 'r') as f:
                data_dict = json.load(f)
                self.data = AssetData(
                    symbol=data_dict['symbol'],
                    price=data_dict['price'],
                    sectors=data_dict.get('sectors'),
                    geography=data_dict.get('geography'),
                    top_holdings=data_dict.get('top_holdings'),
                    quote_type=data_dict.get('quote_type', 'EQUITY')
                )
            return True
        return False
        
    def _load_from_external_provider(self):
        """Fetch data from external data provider and populate symbol data"""
        try:
            # Get the data provider
            provider = DataProviderFactory.create_provider(self.data_provider_name)

            # Fetch data
            response = provider.get_asset_data(self.data.symbol)

            if not response.success:
                print(response.error_message)
                return False

            # Update the asset data with the response
            self.data = AssetData(
                symbol=response.symbol,
                price=response.price,
                sectors=response.sectors,
                geography=response.geography,
                top_holdings=response.top_holdings,
                quote_type=response.quote_type
            )

            return True
        except Exception as e:
            print(f"Error fetching data for {self.data.symbol}: {e}")
            return False
