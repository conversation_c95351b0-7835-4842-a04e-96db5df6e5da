"""
Abstract base class for asset data providers.
This allows for easy swapping between different data sources (Yahoo Finance, Alpha Vantage, etc.)
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict
from dataclasses import dataclass


@dataclass
class AssetDataResponse:
    """Response object for asset data from external APIs"""
    symbol: str
    price: float
    sectors: Optional[Dict[str, float]] = None
    geography: Optional[Dict[str, float]] = None
    top_holdings: Optional[Dict[str, float]] = None
    quote_type: str = 'EQUITY'
    success: bool = True
    error_message: Optional[str] = None


class DataProvider(ABC):
    """Abstract base class for asset data providers"""
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Name of the data provider"""
        pass
    
    @abstractmethod
    def get_asset_data(self, symbol: str) -> AssetDataResponse:
        """
        Fetch comprehensive asset data for a given symbol
        
        Args:
            symbol: Stock/ETF symbol to fetch data for
            
        Returns:
            AssetDataResponse with all available data
        """
        pass
    
    def is_available(self) -> bool:
        """
        Check if the data provider is available (e.g., API key is valid, service is up)
        
        Returns:
            True if provider is available, False otherwise
        """
        return True  # Default implementation


class DataProviderFactory:
    """Factory for creating data provider instances"""
    
    _providers = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class):
        """Register a data provider class"""
        cls._providers[name] = provider_class
    
    @classmethod
    def create_provider(cls, name: str) -> DataProvider:
        """Create a data provider instance by name"""
        if name not in cls._providers:
            raise ValueError(f"Unknown data provider: {name}")
        
        return cls._providers[name]()
    
    @classmethod
    def get_available_providers(cls) -> list:
        """Get list of available provider names"""
        return list(cls._providers.keys())
