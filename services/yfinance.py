import yfinance as yf
import plotly.express as px
import pandas as pd
from typing import Optional, Dict, Tuple
from services.data_provider import <PERSON>Provider, AssetDataResponse, DataProviderFactory


class YFinanceClient(DataProvider):
    """Client for fetching asset data from Yahoo Finance"""

    @property
    def name(self) -> str:
        return "Yahoo Finance"

    def get_asset_data(self, symbol: str) -> AssetDataResponse:
        """
        Fetch comprehensive asset data from Yahoo Finance

        Args:
            symbol: Stock/ETF symbol to fetch data for

        Returns:
            AssetDataResponse with all available data
        """
        try:
            ticker = yf.Ticker(symbol)

            # Get basic price information
            fast_info = ticker.get_fast_info()
            price = fast_info.last_price

            # Initialize response
            response = AssetDataResponse(
                symbol=symbol,
                price=price,
                quote_type='EQUITY'
            )

            # Get quote type and additional data
            response.quote_type = self._get_quote_type(ticker)

            # Get sector allocation
            response.sectors = self._get_sector_allocation(ticker, response.quote_type)

            # Get top holdings (for ETFs/mutual funds)
            if response.quote_type in ['ETF', 'MUTUALFUND']:
                response.top_holdings = self._get_top_holdings(ticker)
            else: 
                response.top_holdings = {symbol: 1.0}

            # Get geographic allocation
            response.geography = self._get_geographic_allocation(ticker, response.quote_type, response.top_holdings)

            # Get asset type classification
            response.asset_type = self._get_asset_type_classification(ticker, response.quote_type)

            return response

        except Exception as e:
            return AssetDataResponse(
                symbol=symbol,
                price=0.0,
                success=False,
                error_message=f"Error fetching data for {symbol}: {str(e)}"
            )

    def _get_quote_type(self, ticker: yf.Ticker) -> str:
        """Get the quote type (EQUITY, ETF, MUTUALFUND, etc.)"""
        try:
            # Try to get from info first
            info = ticker.info
            if info and 'quoteType' in info:
                return info['quoteType']

            # Fallback to funds_data
            funds_data = ticker.funds_data
            if funds_data:
                return funds_data.quote_type()

        except Exception:
            pass

        return 'EQUITY'  # Default fallback

    def _get_sector_allocation(self, ticker: yf.Ticker, quote_type: str) -> Optional[Dict[str, float]]:
        """Get sector allocation for asset"""
        if quote_type == 'EQUITY':
            # For individual stocks, get the sector
            try:
                info = ticker.info
                if info and 'sectorKey' in info:
                    return {info['sectorKey']: 1.0}
            except Exception:
                pass
        else:
            # Use funds_data for sector weightings for ETFs and Mutual Funds
            try:
                funds_data = ticker.get_funds_data()
                if funds_data and hasattr(funds_data, 'sector_weightings'):
                    return funds_data.sector_weightings
            except Exception:
                pass

        return None

    def _get_geographic_allocation(self, ticker: yf.Ticker, quote_type: str, top_holdings: Optional[Dict[str, float]]) -> Optional[Dict[str, float]]:
        """Get geographic allocation based on asset type"""
        try:
            # For equities, get country from ticker info
            if quote_type == 'EQUITY':
                info = ticker.info
                if info and 'country' in info:
                    country = info['country']
                    return {country: 1.0}  # 100% allocation to home country

            # For funds (ETF, MUTUALFUND), try multiple approaches
            else:
                # Derive from top holdings
                if top_holdings:
                    return self._get_countries_from_holdings(top_holdings)

        except Exception:
            pass

        return None

    def _get_countries_from_holdings(self, top_holdings: Dict[str, float]) -> Optional[Dict[str, float]]:
        """Get geographic allocation by looking up countries of top holdings"""
        try:
            country_weights = {}
            found_weight = sum(top_holdings.values())

            # Process each holding to get its country
            for symbol, weight in top_holdings.items():
                try:
                    # Create ticker for the holding
                    holding_ticker = yf.Ticker(symbol)
                    holding_info = holding_ticker.info

                    if holding_info and 'country' in holding_info:
                        country = holding_info['country']

                        if country in country_weights:
                            country_weights[country] += weight
                        else:
                            country_weights[country] = weight
                    else:
                        # Country info not available, add to Unknown
                        if "Unknown" in country_weights:
                            country_weights["Unknown"] += weight
                        else:
                            country_weights["Unknown"] = weight

                except Exception:
                    # Error getting country info, add to Unknown
                    if "Unknown" in country_weights:
                        country_weights["Unknown"] += weight
                    else:
                        country_weights["Unknown"] = weight

            # Add any remaining weight to Unknown (for holdings not in top_holdings)
            if found_weight < 1.0:  # If we don't have 100% coverage
                remaining_weight = 1.0 - found_weight
                if "Unknown" in country_weights:
                    country_weights["Unknown"] += remaining_weight
                else:
                    country_weights["Unknown"] = remaining_weight

            # Return the country allocation if we have any data
            if country_weights:
                return country_weights

        except Exception:
            pass

        return None

    def _get_top_holdings(self, ticker: yf.Ticker) -> Optional[Dict[str, float]]:
        """Get top holdings for ETFs and mutual funds"""
        try:
            # Try funds_data top holdings
            funds_data = ticker.get_funds_data()
            top_holdings_df = funds_data.top_holdings
            top_holdings = top_holdings_df.to_dict()
            return top_holdings['Holding Percent']

        except Exception:
            pass

        return None

    def _get_asset_type_classification(self, ticker: yf.Ticker, quote_type: str) -> Optional[Tuple[int, int]]:
        """
        Get asset type classification as [style, market_cap] tuple

        For ETFs/Mutual Funds: Uses categoryName from funds_overview
        For stocks: Uses marketCap from ticker.info (stubbed for now)

        Returns:
            Tuple[int, int]: [style, market_cap] where:
                style: 0=Value, 1=Blend, 2=Growth
                market_cap: 0=Small, 1=Medium, 2=Large
        """
        try:
            if quote_type in ['ETF', 'MUTUALFUND']:
                return self._classify_fund_type(ticker)
            else:
                return self._classify_stock_type(ticker)
        except Exception:
            return None

    def _classify_fund_type(self, ticker: yf.Ticker) -> Optional[Tuple[int, int]]:
        """Classify ETF/Mutual Fund using categoryName from fund_overview"""
        try:
            funds_data = ticker.get_funds_data()
            if funds_data and hasattr(funds_data, 'fund_overview'):
                fund_overview = funds_data.fund_overview
                category_name = fund_overview.get("categoryName", "").lower()

                category_split = category_name.split(" ")
                if len(category_split) == 2:
                    style_str = category_split[1]
                    cap_str = category_split[0]

                # Map full category name to style and market cap classification
                style = self._to_style(style_str)
                market_cap = self._to_market_cap(cap_str)

                if style is not None and market_cap is not None:
                    return (style, market_cap)

        except Exception:
            pass
        return None

    def _classify_stock_type(self, ticker: yf.Ticker) -> Optional[Tuple[int, int]]:
        """Classify individual stock using marketCap (stubbed implementation)"""
        try:
            info = ticker.info
            if info and 'marketCap' in info:
                market_cap_value = info['marketCap']

                # Classify market cap (these thresholds are common industry standards)
                if market_cap_value < 2_000_000_000:  # < $2B
                    market_cap = 0  # Small
                elif market_cap_value < 10_000_000_000:  # < $10B
                    market_cap = 1  # Medium
                else:
                    market_cap = 2  # Large

                # For stocks, style classification is more complex and would require
                # additional data like P/E ratios, P/B ratios, etc.
                # For now, we'll default to Blend (1) as a stub
                style = 1  # Blend (stubbed)

                return (style, market_cap)

        except Exception:
            pass
        return None

    def _to_style(self, style: str) -> Optional[int]:
        """Map fund category name to style classification"""
        category_split = category_name.split(" ")

        # Value indicators
        if any(keyword in category_split for keyword in ['value', 'dividend', 'income', 'yield']):
            return 0  # Value

        # Growth indicators
        if any(keyword in category_split for keyword in ['growth', 'aggressive', 'momentum', 'technology']):
            return 2  # Growth

        # Default to Blend for most other categories
        return 1  # Blend

    def _to_market_cap(self, cap: str) -> Optional[int]:
        """Map fund category name to market cap classification"""

        # Small cap indicators
        if any(keyword in cap for keyword in ['small', 'micro', 'emerging']):
            return 0  # Small

        # Large cap indicators
        if any(keyword in cap for keyword in ['large', 'mega', 'blue chip']):
            return 2  # Large

        # Mid cap indicators or default to Medium
        return 1  # Medium


# Legacy function for backward compatibility
def get_symbol_info(symbol):
    """Fetch price data and return plotly figure"""
    # This function has issues - tickers, start_date, end_date are not defined
    # Keeping for backward compatibility but should be updated
    try:
        data = yf.download(symbol, period="1y")['Adj Close']
        if isinstance(data, pd.Series):
            data = data.to_frame()

        fig = px.line(data, x=data.index, y=data.columns, title=f"{symbol} Price Over Time")
        fig.update_layout(xaxis_title="Date", yaxis_title="Price (USD)")
        return fig
    except Exception as e:
        # Return empty figure on error
        fig = px.line(title=f"Error loading data for {symbol}")
        return fig


# Convenience function for getting price data
def get_price_data(symbols, period="1y"):
    """
    Fetch price data for multiple symbols

    Args:
        symbols: List of symbols or single symbol
        period: Time period (1d, 5d, 1mo, 3mo, 6mo, 1y, 2y, 5y, 10y, ytd, max)

    Returns:
        DataFrame with price data
    """
    try:
        if isinstance(symbols, str):
            symbols = [symbols]

        data = yf.download(symbols, period=period)['Adj Close']
        return data
    except Exception as e:
        print(f"Error fetching price data: {e}")
        return pd.DataFrame()


# Register the YFinance provider
DataProviderFactory.register_provider("yfinance", YFinanceClient)