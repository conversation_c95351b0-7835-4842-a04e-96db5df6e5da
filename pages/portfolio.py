import dash
from dash import dcc, html, Input, Output, dash_table, callback_context, State
import dash_bootstrap_components as dbc
import pandas as pd
import base64
import io
import os
from app import app
from services.yfinance import get_price_data
from utils.asset import Asset
from utils import global_state
import plotly.express as px
import plotly.graph_objects as go

# Portfolio ingest functions
def parse_input_csv(contents, filename):
    """Parse uploaded CSV content and return as a pandas dataframe"""
    try:
        # Decode the base64 string
        content_type, content_string = contents.split(',')
        decoded = base64.b64decode(content_string)

        # Check file size (limit to 100 KB)
        if len(decoded) > 100000:
            raise ValueError(f"File size too large: {filename}")

        # Read CSV from decoded content
        df = pd.read_csv(io.StringIO(decoded.decode('utf-8')))

        # Validate the CSV has the correct columns
        required_columns = ['Symbol', 'Quantity']
        if not all(column in df.columns for column in required_columns):
            raise ValueError(f"Input CSV must contain the following columns: {', '.join(required_columns)}")

        # Index the dataframe by symbol
        df.set_index('Symbol', inplace=True)

        return df

    except Exception as e:
        raise ValueError(f"Error parsing CSV file: {str(e)}")

def ingest_portfolio(contents, filename):
    """Ingest portfolio data from uploaded CSV and update global state"""
    try:
        # Parse the CSV data
        data = parse_input_csv(contents, filename)

        # Create Asset objects and update global state
        assets = []
        for symbol, row in data.iterrows():
            assets.append(Asset(symbol, row['Quantity']))

        # Update global state
        global_state.set_assets(assets)

        return assets, None  # assets, error

    except Exception as e:
        return [], str(e)  # empty assets, error message

layout = html.Div([
    html.H3("Active Portfolio"),
    
    # File selector and import button
    dbc.Row([
        dbc.Col([
            html.Label("Select Portfolio File:"),
            dcc.Upload(
                id='upload-data',
                children=html.Div([
                    'Drag and Drop or ',
                    html.A('Select Files')
                ]),
                style={
                    'width': '100%',
                    'height': '60px',
                    'lineHeight': '60px',
                    'borderWidth': '1px',
                    'borderStyle': 'dashed',
                    'borderRadius': '5px',
                    'textAlign': 'center',
                    'margin': '10px'
                },
                multiple=False
            )
        ], width=8),
        dbc.Col([
            html.Div([
                dbc.Button(
                    "Import Portfolio",
                    id="import-button",
                    color="primary",
                    size="lg",
                    disabled=True,
                    className="mt-4"
                )
            ], className="d-flex justify-content-center")
        ], width=4)
    ], className="mb-4"),

    # Status message area
    dbc.Row([
        dbc.Col([
            html.Div(id="import-status", className="text-center")
        ], width=12)
    ], className="mb-2"),
    
    # Row of 5 number boxes
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("0", id=f'metric-{i}', className="text-center"),
                    html.P("Total Value", className="text-center text-muted")
                ])
            ])
        ], width=2) for i in range(1, 6)
    ], className="mb-4 justify-content-center"),
    
    # 3 pie charts
    dbc.Row([
        dbc.Col([
            dcc.Graph(id='pie-chart-1', figure={})
        ], width=4),
        dbc.Col([
            dcc.Graph(id='pie-chart-2', figure={})
        ], width=4),
        dbc.Col([
            dcc.Graph(id='pie-chart-3', figure={})
        ], width=4)
    ], className="mb-4"),
    
    # Data table
    dbc.Row([
        dbc.Col([
            dash_table.DataTable(
                id='portfolio-table',
                columns=[],
                data=[],
                filter_action="native",
                sort_action="native",
                page_action="native",
                page_current=0,
                page_size=10,
                style_cell={'textAlign': 'left'},
                style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'}
            )
        ], width=12)
    ]),
    
    # Hidden div to store filtered data
    html.Div(id='filtered-data', style={'display': 'none'})
])

# Note: Pie chart cross-filtering functionality will be implemented later
# Currently handled by the main import callback

# Add callback for file upload handling and import button state
@app.callback(
    [Output('upload-data', 'children'),
     Output('import-button', 'disabled')],
    Input('upload-data', 'filename')
)
def update_upload_display(filename):
    if filename:
        return (html.Div([
            html.Div([
                html.I(className="fas fa-file-csv me-2"),
                html.Span(f"Selected: {filename}", className="me-3"),
                html.Button("Choose Different File",
                           className="btn btn-outline-secondary btn-sm",
                           id="change-file-btn")
            ], className="d-flex align-items-center justify-content-center")
        ]), False)  # Enable import button
    else:
        return (html.Div([
            'Drag and Drop or ',
            html.A('Select Files')
        ]), True)  # Disable import button

# Add callback to reset file selection
@app.callback(
    Output('upload-data', 'contents'),
    Input('change-file-btn', 'n_clicks'),
    prevent_initial_call=True
)
def reset_file_selection(n_clicks):
    if n_clicks:
        return None
    return dash.no_update

# Callback to handle import button click and portfolio ingestion
@app.callback(
    [Output('portfolio-table', 'columns'),
     Output('portfolio-table', 'data'),
     Output('metric-1', 'children'),
     Output('metric-2', 'children'),
     Output('metric-3', 'children'),
     Output('metric-4', 'children'),
     Output('metric-5', 'children'),
     Output('pie-chart-1', 'figure'),
     Output('pie-chart-2', 'figure'),
     Output('pie-chart-3', 'figure'),
     Output('import-status', 'children')],
    [Input('import-button', 'n_clicks')],
    [State('upload-data', 'contents'),
     State('upload-data', 'filename')]
)
def update_portfolio_from_import(n_clicks, contents, filename):
    """Handle import button click and update portfolio display"""
    if n_clicks is None or contents is None:
        # Return empty state
        return [], [], "0", "0", "0", "0", "0", {}, {}, {}, ""

    try:
        # Ingest the portfolio
        assets, error = ingest_portfolio(contents, filename)

        if error:
            # Return error state with error message
            error_msg = dbc.Alert(f"Import failed: {error}", color="danger", dismissable=True)
            return [], [], "Error", "Error", "Error", "Error", "Error", {}, {}, {}, error_msg

        # Prepare table data
        table_columns = [
            {"name": "Symbol", "id": "symbol"},
            {"name": "Quantity", "id": "quantity"},
            {"name": "Price", "id": "price", "type": "numeric", "format": {"specifier": "$.2f"}},
            {"name": "Value", "id": "value", "type": "numeric", "format": {"specifier": "$.2f"}},
            {"name": "Sector", "id": "sector"},
            {"name": "Country", "id": "country"}
        ]

        table_data = []
        total_value = 0
        sector_allocation = {}
        country_allocation = {}

        for asset in assets:
            # Get asset data (this will fetch from cache or external provider)
            asset_data = asset._get_or_update_data()

            price = asset_data.price if asset_data else 0
            value = asset.base_quantity * price
            total_value += value

            # Get primary sector and country for display
            primary_sector = "Unknown"
            primary_country = "Unknown"

            if asset_data and asset_data.sectors:
                primary_sector = max(asset_data.sectors.items(), key=lambda x: x[1])[0]
                # Accumulate weighted sector allocations by cash value
                # Each sector gets: (asset_total_value * sector_weight_in_asset)
                for sector, weight in asset_data.sectors.items():
                    sector_value = value * weight
                    sector_allocation[sector] = sector_allocation.get(sector, 0) + sector_value

            if asset_data and asset_data.geography:
                primary_country = max(asset_data.geography.items(), key=lambda x: x[1])[0]
                # Accumulate weighted geographic allocations by cash value
                # Each country gets: (asset_total_value * country_weight_in_asset)
                for country, weight in asset_data.geography.items():
                    country_value = value * weight
                    country_allocation[country] = country_allocation.get(country, 0) + country_value

            table_data.append({
                "symbol": asset.data.symbol,
                "quantity": asset.base_quantity,
                "price": price,
                "value": value,
                "sector": primary_sector,
                "country": primary_country
            })

        # Create weighted pie charts based on cash value
        sector_fig = create_pie_chart(sector_allocation, "Sector Allocation")
        country_fig = create_pie_chart(country_allocation, "Geographic Allocation")

        # Calculate metrics
        num_assets = len(assets)
        avg_value = total_value / num_assets if num_assets > 0 else 0

        # Success message
        success_msg = dbc.Alert(f"Successfully imported {num_assets} assets from {filename}",
                               color="success", dismissable=True)

        return (table_columns, table_data,
                f"${total_value:,.2f}", f"{num_assets}", f"${avg_value:,.2f}", "0", "0",
                sector_fig, country_fig, {}, success_msg)

    except Exception as e:
        # Return error state
        error_msg = dbc.Alert(f"Import failed: {str(e)}", color="danger", dismissable=True)
        return [], [], f"Error: {str(e)}", "Error", "Error", "Error", "Error", {}, {}, {}, error_msg

def create_pie_chart(data_dict, title):
    """Create a weighted pie chart from a dictionary of cash values"""
    if not data_dict:
        return {"data": [], "layout": {"title": title}}

    labels = list(data_dict.keys())
    values = list(data_dict.values())

    fig = px.pie(
        values=values,
        names=labels,
        title=f"{title} (by Cash Value)",
        hover_name=labels
    )

    # Update traces to show percentages in the pie slices and custom hover info
    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate='<b>%{label}</b><br>' +
                      'Value: $%{value:,.2f}<br>' +
                      'Percentage: %{percent}<br>' +
                      '<extra></extra>',
        textfont_size=10
    )

    # Update layout for better appearance
    fig.update_layout(
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.01
        ),
        margin=dict(l=20, r=120, t=50, b=20)
    )

    return fig
