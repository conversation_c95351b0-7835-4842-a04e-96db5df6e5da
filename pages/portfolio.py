import dash
from dash import dcc, html, Input, Output, dash_table, callback_context, State
import dash_bootstrap_components as dbc
import pandas as pd
import base64
import io
import os
from app import app
from services.yfinance import get_price_data
from utils.asset import Asset
from utils import global_state
import plotly.express as px
import plotly.graph_objects as go

# Portfolio ingest functions
def parse_input_csv(contents, filename):
    """Parse uploaded CSV content and return as a pandas dataframe"""
    try:
        # Decode the base64 string
        content_type, content_string = contents.split(',')
        decoded = base64.b64decode(content_string)

        # Check file size (limit to 100 KB)
        if len(decoded) > 100000:
            raise ValueError(f"File size too large: {filename}")

        # Read CSV from decoded content
        df = pd.read_csv(io.StringIO(decoded.decode('utf-8')))

        # Validate the CSV has the correct columns
        required_columns = ['Symbol', 'Quantity']
        if not all(column in df.columns for column in required_columns):
            raise ValueError(f"Input CSV must contain the following columns: {', '.join(required_columns)}")

        # Index the dataframe by symbol
        df.set_index('Symbol', inplace=True)

        return df

    except Exception as e:
        raise ValueError(f"Error parsing CSV file: {str(e)}")

def ingest_portfolio(contents, filename):
    """Ingest portfolio data from uploaded CSV and update global state"""
    try:
        # Parse the CSV data
        data = parse_input_csv(contents, filename)

        # Separate cash entries from asset entries
        assets = []
        total_cash = 0

        for symbol, row in data.iterrows():
            if symbol.upper() == "**CASH**":
                # Handle cash entries - assume quantity represents cash amount
                total_cash += float(row['Quantity'])
            else:
                # Handle regular asset entries
                assets.append(Asset(symbol, row['Quantity']))

        # Update global state
        global_state.set_assets(assets)

        return assets, total_cash, None  # assets, cash, error

    except Exception as e:
        return [], 0, str(e)  # empty assets, no cash, error message

layout = html.Div([
    html.H3("Active Portfolio"),
    
    # File selector and import button - inline layout
    dbc.Row([
        dbc.Col([
            html.Div([
                html.Label("Select Portfolio File:", className="me-3 align-self-center mb-0"),
                dcc.Upload(
                    id='upload-data',
                    children=html.Div([
                        'Drag and Drop or ',
                        html.A('Select Files')
                    ]),
                    style={
                        'width': '500px',
                        'height': '40px',
                        'lineHeight': '40px',
                        'borderWidth': '1px',
                        'borderStyle': 'dashed',
                        'borderRadius': '5px',
                        'textAlign': 'center',
                        'display': 'inline-block',
                        'marginRight': '15px'
                    },
                    multiple=False
                ),
                dbc.Button(
                    "Import Portfolio",
                    id="import-button",
                    color="primary",
                    size="sm",
                    disabled=True,
                    className="align-self-center"
                )
            ], className="d-flex align-items-center")
        ], width=12)
    ], className="mb-4"),

    # Status message area
    dbc.Row([
        dbc.Col([
            html.Div(id="import-status", className="text-center")
        ], width=12)
    ], className="mb-2"),
    
    # Row of 3 metric cards (hidden until data loaded)
    dbc.Row([
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("$0", id='total-assets-metric', className="text-center mb-1"),
                    html.P("Total Assets", className="text-center text-muted mb-0")
                ], className="py-3")
            ])
        ], width=4),
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("$0", id='total-cash-metric', className="text-center mb-1"),
                    html.P("Total Cash", className="text-center text-muted mb-0")
                ], className="py-3")
            ])
        ], width=4),
        dbc.Col([
            dbc.Card([
                dbc.CardBody([
                    html.H4("0%", id='cash-percentage-metric', className="text-center mb-1"),
                    html.P("Cash Percentage", className="text-center text-muted mb-0")
                ], className="py-3")
            ])
        ], width=4)
    ], id="metrics-row", className="mb-4 justify-content-center", style={'display': 'none'}),
    
    # 2x2 grid of charts (hidden until data loaded)
    html.Div([
        dbc.Row([
            dbc.Col([
                dcc.Graph(id='quote-type-chart', figure={})
            ], width=6),
            dbc.Col([
                dcc.Graph(id='sector-chart', figure={})
            ], width=6)
        ], className="mb-4"),
        dbc.Row([
            dbc.Col([
                dcc.Graph(id='asset-type-grid', figure={})
            ], width=6),
            dbc.Col([
                dcc.Graph(id='country-chart', figure={})
            ], width=6)
        ], className="mb-4")
    ], id="charts-section", style={'display': 'none'}),
    
    # Data table (hidden until data loaded)
    dbc.Row([
        dbc.Col([
            dash_table.DataTable(
                id='portfolio-table',
                columns=[],
                data=[],
                filter_action="native",
                sort_action="native",
                page_action="native",
                page_current=0,
                page_size=10,
                style_cell={'textAlign': 'left'},
                style_header={'backgroundColor': 'rgb(230, 230, 230)', 'fontWeight': 'bold'}
            )
        ], width=12)
    ], id="table-section", className="mb-5", style={'display': 'none'}),

    # Buffer space
    html.Div(style={'height': '20px'}),

    # Hidden div to store filtered data
    html.Div(id='filtered-data', style={'display': 'none'})
])

# Note: Pie chart cross-filtering functionality will be implemented later
# Currently handled by the main import callback

# Add callback for file upload handling and import button state
@app.callback(
    [Output('upload-data', 'children'),
     Output('import-button', 'disabled')],
    Input('upload-data', 'filename')
)
def update_upload_display(filename):
    if filename:
        return (html.Div([
            html.Div([
                html.I(className="fas fa-file-csv me-2"),
                html.Span(f"{filename}", className="me-3"),
                html.Button("Choose Different File",
                           className="btn btn-outline-secondary btn-sm",
                           id="change-file-btn")
            ], className="d-flex align-items-center justify-content-center")
        ]), False)  # Enable import button
    else:
        return (html.Div([
            'Drag and Drop or ',
            html.A('Select Files')
        ]), True)  # Disable import button

# Add callback to reset file selection
@app.callback(
    Output('upload-data', 'contents'),
    Input('change-file-btn', 'n_clicks'),
    prevent_initial_call=True
)
def reset_file_selection(n_clicks):
    if n_clicks:
        return None
    return dash.no_update

# Callback to handle import button click and portfolio ingestion
@app.callback(
    [Output('portfolio-table', 'columns'),
     Output('portfolio-table', 'data'),
     Output('total-assets-metric', 'children'),
     Output('total-cash-metric', 'children'),
     Output('cash-percentage-metric', 'children'),
     Output('quote-type-chart', 'figure'),
     Output('sector-chart', 'figure'),
     Output('asset-type-grid', 'figure'),
     Output('country-chart', 'figure'),
     Output('metrics-row', 'style'),
     Output('charts-section', 'style'),
     Output('table-section', 'style'),
     Output('import-status', 'children')],
    [Input('import-button', 'n_clicks')],
    [State('upload-data', 'contents'),
     State('upload-data', 'filename')]
)
def update_portfolio_from_import(n_clicks, contents, filename):
    """Handle import button click and update portfolio display"""
    if n_clicks is None or contents is None:
        # Return empty state
        # Hide all sections when no data
        hidden_style = {'display': 'none'}
        return [], [], "$0", "$0", "0%", {}, {}, {}, {}, hidden_style, hidden_style, hidden_style, ""

    try:
        # Ingest the portfolio
        assets, total_cash, error = ingest_portfolio(contents, filename)

        if error:
            # Return error state with error message
            error_msg = dbc.Alert(f"Import failed: {error}", color="danger", dismissable=True)
            # Hide all sections on error
            hidden_style = {'display': 'none'}
            return [], [], "Error", "Error", "Error", {}, {}, {}, {}, hidden_style, hidden_style, hidden_style, error_msg

        # Prepare table data
        table_columns = [
            {"name": "Symbol", "id": "symbol"},
            {"name": "Quantity", "id": "quantity"},
            {"name": "Price", "id": "price", "type": "numeric", "format": {"specifier": "$.2f"}},
            {"name": "Value", "id": "value", "type": "numeric", "format": {"specifier": "$.2f"}},
            {"name": "Sector", "id": "sector"},
            {"name": "Country", "id": "country"}
        ]

        table_data = []
        total_value = 0
        sector_allocation = {}
        country_allocation = {}
        quote_type_allocation = {}
        asset_values = []  # For asset type grid weighting

        for asset in assets:
            # Get asset data (this will fetch from cache or external provider)
            asset_data = asset._get_or_update_data()

            price = asset_data.price if asset_data else 0
            value = asset.base_quantity * price
            total_value += value

            # Get primary sector and country for display
            primary_sector = "Unknown"
            primary_country = "Unknown"

            if asset_data and asset_data.sectors:
                primary_sector = max(asset_data.sectors.items(), key=lambda x: x[1])[0]
                # Accumulate weighted sector allocations by cash value
                # Each sector gets: (asset_total_value * sector_weight_in_asset)
                for sector, weight in asset_data.sectors.items():
                    sector_value = value * weight
                    sector_allocation[sector] = sector_allocation.get(sector, 0) + sector_value

            if asset_data and asset_data.geography:
                primary_country = max(asset_data.geography.items(), key=lambda x: x[1])[0]
                # Accumulate weighted geographic allocations by cash value
                # Each country gets: (asset_total_value * country_weight_in_asset)
                for country, weight in asset_data.geography.items():
                    country_value = value * weight
                    country_allocation[country] = country_allocation.get(country, 0) + country_value

            # Accumulate quote type allocation
            quote_type = asset_data.quote_type if asset_data else 'UNKNOWN'
            quote_type_allocation[quote_type] = quote_type_allocation.get(quote_type, 0) + value

            # Store asset value for grid weighting
            asset_values.append(value)

            table_data.append({
                "symbol": asset.data.symbol,
                "quantity": asset.base_quantity,
                "price": price,
                "value": value,
                "sector": primary_sector,
                "country": primary_country
            })

        # Create charts
        quote_type_fig = create_pie_chart(quote_type_allocation, "Quote Type Allocation")
        sector_fig = create_bar_chart(sector_allocation, "Sector Allocation")
        asset_type_fig = create_asset_type_grid_chart(assets, asset_values)
        country_fig = create_bar_chart(country_allocation, "Geographic Allocation")

        # Calculate metrics
        num_assets = len(assets)
        total_portfolio_value = total_value + total_cash
        cash_percentage = (total_cash / total_portfolio_value * 100) if total_portfolio_value > 0 else 0

        # Success message
        cash_msg = f" and ${total_cash:,.2f} in cash" if total_cash > 0 else ""
        success_msg = dbc.Alert(f"Successfully imported {num_assets} assets{cash_msg} from {filename}",
                               color="success", dismissable=True)

        # Show all sections when data is loaded
        visible_style = {'display': 'block'}

        return (table_columns, table_data,
                f"${total_value:,.2f}", f"${total_cash:,.2f}", f"{cash_percentage:.1f}%",
                quote_type_fig, sector_fig, asset_type_fig, country_fig,
                visible_style, visible_style, visible_style, success_msg)

    except Exception as e:
        # Return error state
        error_msg = dbc.Alert(f"Import failed: {str(e)}", color="danger", dismissable=True)
        return [], [], f"Error: {str(e)}", "Error", "Error", "Error", "Error", {}, {}, {}, error_msg

def create_pie_chart(data_dict, title):
    """Create a weighted pie chart from a dictionary of cash values"""
    if not data_dict:
        return {"data": [], "layout": {"title": title}}

    labels = list(data_dict.keys())
    values = list(data_dict.values())

    fig = px.pie(
        values=values,
        names=labels,
        title=f"{title} (by Cash Value)",
        hover_name=labels
    )

    # Update traces to show percentages in the pie slices and custom hover info
    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate='<b>%{label}</b><br>' +
                      'Value: $%{value:,.2f}<br>' +
                      'Percentage: %{percent}<br>' +
                      '<extra></extra>',
        textfont_size=10
    )

    # Update layout for better appearance
    fig.update_layout(
        showlegend=True,
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.01
        ),
        margin=dict(l=20, r=120, t=50, b=20)
    )

    return fig


def create_bar_chart(data_dict, title):
    """Create a horizontal bar chart from a dictionary of cash values"""
    if not data_dict:
        return {"data": [], "layout": {"title": title}}

    # Sort by value for better visualization
    sorted_items = sorted(data_dict.items(), key=lambda x: x[1], reverse=True)
    labels = [item[0] for item in sorted_items]
    values = [item[1] for item in sorted_items]

    fig = px.bar(
        x=values,
        y=labels,
        orientation='h',
        title=f"{title} (by Cash Value)",
        labels={'x': 'Value ($)', 'y': ''}
    )

    # Update traces for better hover info
    fig.update_traces(
        hovertemplate='<b>%{y}</b><br>' +
                      'Value: $%{x:,.2f}<br>' +
                      '<extra></extra>'
    )

    # Update layout for better appearance
    fig.update_layout(
        showlegend=False,
        margin=dict(l=120, r=20, t=50, b=20),
        height=400
    )

    return fig


def create_asset_type_grid_chart(assets, asset_values):
    """Create a heatmap showing asset type classification grid"""
    if not assets:
        return {"data": [], "layout": {"title": "Asset Type Classification Grid"}}

    # Import Asset class to use the grid creation method
    from utils.asset import Asset

    # Create the grid with cash value weights
    grid_data = Asset.create_asset_type_grid(assets, asset_values)
    grid = grid_data['grid']
    labels = grid_data['labels']

    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=grid,
        x=labels['x_labels'],  # Style: Value, Blend, Growth
        y=labels['y_labels'],  # Market Cap: Small, Medium, Large
        colorscale='Blues',
        showscale=True,
        hovertemplate='<b>%{y} Cap %{x}</b><br>' +
                      'Total Value: $%{z:,.2f}<br>' +
                      '<extra></extra>'
    ))

    fig.update_layout(
        title="Asset Type Classification Grid (by Cash Value)",
        xaxis_title="Investment Style",
        yaxis_title="Market Capitalization",
        height=400,
        margin=dict(l=80, r=20, t=50, b=50)
    )

    return fig
